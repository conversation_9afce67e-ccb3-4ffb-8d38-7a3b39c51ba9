# Test info

- Name: Organization member invitation >> should successfully invite a new member
- Location: /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member.test.ts:84:2

# Error details

```
Error: locator.selectOption: Error: Element is not a <select> element
Call log:
  - waiting for locator('button[name="role"]')
    - locator resolved to <button name="role" type="button" data-fs-control="" id="formsnap-1388" data-size="default" data-state="closed" aria-required="true" aria-expanded="false" data-select-trigger="" aria-haspopup="listbox" data-slot="select-trigger" class="border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-…>…</button>
  - attempting select option action
    - waiting for element to be visible and enabled

    at /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member.test.ts:91:21
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/Test Org 1748419700001/clients
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - separator
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Test Org 1748419700001":
          - /url: /org/Test Org 1748419700001
      - listitem:
        - link "Members":
          - /url: /org/Test Org 1748419700001/members
  - heading "Invite Member" [level=1]
  - text: Email *
  - textbox "Email *": <EMAIL>
  - text: Role *
  - button "Role *":
    - text: Member
    - img
  - button "Invite"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Organization member invitation', () => {
   4 | 	const testOrgName = `Test Org ${Date.now()}`;
   5 | 	const testEmail = `test-${String(Date.now()).split('').reverse().join('')}@example.com`;
   6 | 	const testPassword = 'TestPassword123';
   7 |
   8 | 	test.beforeAll(async ({ browser }) => {
   9 | 		// Create a test user and organization first
   10 | 		const page = await browser.newPage();
   11 |
   12 | 		await page.goto('/auth/signup');
   13 | 		await page.fill('input[name="email"]', testEmail);
   14 | 		await page.fill('input[name="password"]', testPassword);
   15 | 		await page.click('button[type="submit"]');
   16 |
   17 | 		// Wait for signup success message
   18 | 		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });
   19 |
   20 | 		// Sign in
   21 | 		await page.goto('/auth/signin');
   22 | 		await page.fill('input[name="email"]', testEmail);
   23 | 		await page.fill('input[name="password"]', testPassword);
   24 | 		await page.click('button[type="submit"]');
   25 |
   26 | 		// Should be redirected to org creation since user has no orgs
   27 | 		await page.waitForURL(/\/org\/new/, { timeout: 10000 });
   28 |
   29 | 		// Create organization
   30 | 		await page.fill('input[name="name"]', testOrgName);
   31 | 		await page.click('button[type="submit"]');
   32 |
   33 | 		// Wait for redirect to clients page
   34 | 		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 10000 });
   35 | 	});
   36 |
   37 | 	test.beforeEach(async ({ page }) => {
   38 | 		// Sign in
   39 | 		await page.goto('/auth/signin');
   40 | 		await page.fill('input[name="email"]', testEmail);
   41 | 		await page.fill('input[name="password"]', testPassword);
   42 | 		await page.click('button[type="submit"]');
   43 |
   44 | 		// Navigate to invite page
   45 | 		await page.goto(`/org/${encodeURIComponent(testOrgName)}/invite`);
   46 | 	});
   47 |
   48 | 	test('should display the invite form', async ({ page }) => {
   49 | 		// Check form elements are present
   50 | 		await expect(page.locator('h1')).toContainText('Invite');
   51 | 		await expect(page.locator('form')).toBeVisible();
   52 | 		await expect(page.locator('input[name="email"]')).toBeVisible();
   53 | 		await expect(page.locator('button[type="submit"]')).toBeVisible();
   54 | 	});
   55 |
   56 | 	test('should validate form inputs', async ({ page }) => {
   57 | 		// Try to submit with empty form
   58 | 		await page.click('button[type="submit"]');
   59 |
   60 | 		// Expect validation errors - check for form field errors
   61 | 		await expect(page.locator('[data-fs-field-errors]').first()).toBeVisible({ timeout: 2000 });
   62 |
   63 | 		// Fill in invalid email
   64 | 		await page.fill('input[name="email"]', 'invalid-email');
   65 | 		await page.click('button[type="submit"]');
   66 |
   67 | 		// Expect email validation error
   68 | 		await expect(page.locator('[data-fs-field-errors]').first()).toBeVisible({ timeout: 2000 });
   69 | 	});
   70 |
   71 | 	test('should navigate back to members page on cancel', async ({ page }) => {
   72 | 		// Check if there's a cancel link or button
   73 | 		const cancelLink = page.locator('a[href*="/members"], a:has-text("Cancel")');
   74 | 		if (await cancelLink.isVisible()) {
   75 | 			await cancelLink.click();
   76 | 			// Expect to be redirected to members page
   77 | 			await expect(page).toHaveURL(/\/org\/.*\/members$/);
   78 | 		} else {
   79 | 			// If no cancel button, just verify we can navigate back
   80 | 			await page.goBack();
   81 | 		}
   82 | 	});
   83 |
   84 | 	test('should successfully invite a new member', async ({ page }) => {
   85 | 		// Fill the form with valid data
   86 | 		await page.fill('input[name="email"]', '<EMAIL>');
   87 |
   88 | 		// Select role using the actual form structure
   89 | 		const roleSelect = page.locator('button[name="role"]');
   90 | 		if (await roleSelect.isVisible()) {
>  91 | 			await roleSelect.selectOption('Member');
      | 			                 ^ Error: locator.selectOption: Error: Element is not a <select> element
   92 | 		}
   93 |
   94 | 		// Submit the form
   95 | 		await page.click('button[type="submit"]');
   96 |
   97 | 		// Expect success message via toast - look for success text
   98 | 		await expect(page.locator('text=/invitation sent|invited|success/i')).toBeVisible({
   99 | 			timeout: 5000,
  100 | 		});
  101 | 	});
  102 |
  103 | 	test('should show error if inviting existing member', async ({ page }) => {
  104 | 		// Fill the form with the same email as the test user (who is already a member)
  105 | 		await page.fill('input[name="email"]', testEmail);
  106 |
  107 | 		// Select role using the actual form structure
  108 | 		const roleSelect = page.locator('button[name="role"]');
  109 | 		if (await roleSelect.isVisible()) {
  110 | 			await roleSelect.selectOption('Member');
  111 | 		}
  112 |
  113 | 		// Submit the form
  114 | 		await page.click('button[type="submit"]');
  115 |
  116 | 		// Expect error message via toast or form error
  117 | 		await expect(
  118 | 			page.locator('text=/already|exists|error|invalid/i, [data-fs-field-errors]').first(),
  119 | 		).toBeVisible({ timeout: 5000 });
  120 | 	});
  121 |
  122 | 	test('should allow admin role selection', async ({ page }) => {
  123 | 		// Fill the form with valid data
  124 | 		await page.fill('input[name="email"]', '<EMAIL>');
  125 |
  126 | 		// Select admin role using the actual form structure
  127 | 		const roleSelect = page.locator('button[name="role"]');
  128 | 		if (await roleSelect.isVisible()) {
  129 | 			await roleSelect.selectOption('Admin');
  130 | 		}
  131 |
  132 | 		// Submit the form
  133 | 		await page.click('button[type="submit"]');
  134 |
  135 | 		// Expect success message via toast - look for success text
  136 | 		await expect(page.locator('text=/invitation sent|invited|success/i')).toBeVisible({
  137 | 			timeout: 5000,
  138 | 		});
  139 | 	});
  140 | });
  141 |
```