import { test, expect } from '@playwright/test';

test.describe('Organization member invitation', () => {
	const testOrgName = `Test Org ${Date.now()}`;
	const testEmail = `test-${String(Date.now()).split('').reverse().join('')}@example.com`;
	const testPassword = 'TestPassword123';

	test.beforeAll(async ({ browser }) => {
		// Create a test user and organization first
		const page = await browser.newPage();

		await page.goto('/auth/signup');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Wait for signup success message
		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });

		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Should be redirected to org creation since user has no orgs
		await page.waitForURL(/\/org\/new/, { timeout: 10000 });

		// Create organization
		await page.fill('input[name="name"]', testOrgName);
		await page.click('button[type="submit"]');

		// Wait for redirect to clients page
		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 10000 });
	});

	test.beforeEach(async ({ page }) => {
		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Navigate to invite page
		await page.goto(`/org/${encodeURIComponent(testOrgName)}/invite`);
	});

	test('should display the invite form', async ({ page }) => {
		// Check form elements are present
		await expect(page.locator('h1')).toContainText('Invite');
		await expect(page.locator('form')).toBeVisible();
		await expect(page.locator('input[name="email"]')).toBeVisible();
		await expect(page.locator('button[type="submit"]')).toBeVisible();
	});

	test('should validate form inputs', async ({ page }) => {
		// Try to submit with empty form
		await page.click('button[type="submit"]');

		// Expect validation errors - check for form field errors
		await expect(page.locator('[data-fs-field-errors]').first()).toBeVisible({ timeout: 2000 });

		// Fill in invalid email
		await page.fill('input[name="email"]', 'invalid-email');
		await page.click('button[type="submit"]');

		// Expect email validation error
		await expect(page.locator('[data-fs-field-errors]').first()).toBeVisible({ timeout: 2000 });
	});

	test('should navigate back to members page on cancel', async ({ page }) => {
		// Check if there's a cancel link or button
		const cancelLink = page.locator('a[href*="/members"], a:has-text("Cancel")');
		if (await cancelLink.isVisible()) {
			await cancelLink.click();
			// Expect to be redirected to members page
			await expect(page).toHaveURL(/\/org\/.*\/members$/);
		} else {
			// If no cancel button, just verify we can navigate back
			await page.goBack();
		}
	});

	test('should successfully invite a new member', async ({ page }) => {
		// Fill the form with valid data
		await page.fill('input[name="email"]', '<EMAIL>');

		// Select role using the actual form structure
		const roleSelect = page.locator('button[name="role"]');
		if (await roleSelect.isVisible()) {
			await roleSelect.selectOption('Member');
		}

		// Submit the form
		await page.click('button[type="submit"]');

		// Expect success message via toast - look for success text
		await expect(page.locator('text=/invitation sent|invited|success/i')).toBeVisible({
			timeout: 5000,
		});
	});

	test('should show error if inviting existing member', async ({ page }) => {
		// Fill the form with the same email as the test user (who is already a member)
		await page.fill('input[name="email"]', testEmail);

		// Select role using the actual form structure
		const roleSelect = page.locator('button[name="role"]');
		if (await roleSelect.isVisible()) {
			await roleSelect.selectOption('Member');
		}

		// Submit the form
		await page.click('button[type="submit"]');

		// Expect error message via toast or form error
		await expect(
			page.locator('text=/already|exists|error|invalid/i, [data-fs-field-errors]').first(),
		).toBeVisible({ timeout: 5000 });
	});

	test('should allow admin role selection', async ({ page }) => {
		// Fill the form with valid data
		await page.fill('input[name="email"]', '<EMAIL>');

		// Select admin role using the actual form structure
		const roleSelect = page.locator('button[name="role"]');
		if (await roleSelect.isVisible()) {
			await roleSelect.selectOption('Admin');
		}

		// Submit the form
		await page.click('button[type="submit"]');

		// Expect success message via toast - look for success text
		await expect(page.locator('text=/invitation sent|invited|success/i')).toBeVisible({
			timeout: 5000,
		});
	});
});
